package com.eci.common;

import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.page.TgPageInfo;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class ResponseMsgUtilX {


    /**
     * 处理单个实体或分页返回对象（适配 TgPageInfo 的场景）
     *
     * @param code 返回码
     * @param data 数据对象（支持单个实体、列表或 TgPageInfo）
     * @param <T>  数据类型
     * @return 修改后的 ResponseMsg
     */
    public static <T> ResponseMsg success(int code, T data) {
        if (data != null) {
            // 如果是 TgPageInfo 类型
            if (data instanceof TgPageInfo) {
                handleTgPageInfo((TgPageInfo<?>) data);
            }
            // 如果是 List 类型
            else if (data instanceof List) {
                DictFieldUtils.handleDictFields((List<?>) data);
            }
            // 如果是单个实体
            else {
                DictFieldUtils.handleDictFields(data);
            }
        }

        // 调用原有的 success 方法
        return ResponseMsgUtil.success(code, data);
    }

    /**
     * 处理分页对象 TgPageInfo
     *
     * @param tgPageInfo 包含分页数据的对象
     */
    private static void handleTgPageInfo(TgPageInfo<?> tgPageInfo) {
        if (tgPageInfo == null || tgPageInfo.getList() == null) {
            return;
        }

        // 处理分页数据中的列表
        List<?> list = tgPageInfo.getList();
        DictFieldUtils.handleDictFields(list);
    }

//    /**
//     * 重写 success 方法，添加 DictFieldUtils.handleDictField 调用
//     *
//     * @param code   返回码
//     * @param entity 实体对象
//     * @param <T>    实体类型
//     * @return 修改后的 ResponseMsg
//     */
//    public static <T> ResponseMsg success(int code, T entity) {
//        // 调用 DictFieldUtils 处理实体
//        if (entity != null) {
//            DictFieldUtils.handleDictField(entity);
//        }
//
//        // 调用原有的 success 方法生成 ResponseMsg
//        ResponseMsg responseMsg = ResponseMsgUtil.success(code, entity);
//
//        // 返回处理后的 ResponseMsg
//        return responseMsg;
//    }

    /**
     * 重载的 success 方法，适配原有的 success(T object) 方法
     *
     * @param entity 实体对象
     * @param <T>    实体类型
     * @return 修改后的 ResponseMsg
     */
    public static <T> ResponseMsg success(T entity) {
        // 调用 DictFieldUtils 处理实体
        if (entity != null) {
            DictFieldUtils.handleDictFields(entity);
        }

        // 调用原有的 success 方法生成 ResponseMsg
        return ResponseMsgUtil.success(entity);
    }
}
package com.eci.common.cache.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.eci.cache.config.TgCacheHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;

/**
 * Redis 缓存适配器实现
 * 基于 TgCacheHelper 提供 Redis 缓存功能，支持降级到本地缓存
 */
public class RedisCacheAdapter<K, V> implements CacheAdapter<K, V> {

    private static final Logger logger = LoggerFactory.getLogger(RedisCacheAdapter.class);

    private final String keyPrefix;
    private final long defaultExpireTime;
    private final TimeUnit defaultTimeUnit;
    private final Class<V> valueType;
    private final LocalCacheAdapter<K, V> fallbackCache;
    
    // 统计信息
    private final AtomicLong hitCount = new AtomicLong(0);
    private final AtomicLong missCount = new AtomicLong(0);
    private final AtomicLong loadCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    
    // 静态缓存键集合（永不过期的键）
    private final Set<String> permanentKeys = ConcurrentHashMap.newKeySet();

    /**
     * 构造函数
     * 
     * @param keyPrefix 缓存键前缀
     * @param defaultExpireTime 默认过期时间
     * @param defaultTimeUnit 默认时间单位
     * @param valueType 值类型
     */
    public RedisCacheAdapter(String keyPrefix, long defaultExpireTime, TimeUnit defaultTimeUnit, Class<V> valueType) {
        this.keyPrefix = keyPrefix != null ? keyPrefix : "cache:";
        this.defaultExpireTime = defaultExpireTime;
        this.defaultTimeUnit = defaultTimeUnit;
        this.valueType = valueType;
        
        // 创建降级缓存
        this.fallbackCache = new LocalCacheAdapter<>(1000, defaultExpireTime, defaultTimeUnit, true);
        
        logger.info("RedisCacheAdapter 初始化完成: keyPrefix={}, expireTime={}{}",
                this.keyPrefix, defaultExpireTime, defaultTimeUnit);
    }

    /**
     * 使用默认配置的构造函数
     */
    @SuppressWarnings("unchecked")
    public RedisCacheAdapter(String keyPrefix, Class<V> valueType) {
        this(keyPrefix, 60, TimeUnit.MINUTES, valueType);
    }

    @Override
    public V get(K key) {
        if (key == null) {
            return null;
        }

        String redisKey = buildRedisKey(key);
        
        try {
            // 检查是否为永久缓存
            if (permanentKeys.contains(redisKey)) {
                Object valueObj = TgCacheHelper.get(redisKey);
                if (valueObj != null) {
                    String value = valueObj.toString();
                    hitCount.incrementAndGet();
                    return deserializeValue(value);
                }
            } else {
                // 检查普通缓存
                if (TgCacheHelper.hasKey(redisKey)) {
                    Object valueObj = TgCacheHelper.get(redisKey);
                    if (valueObj != null) {
                        String value = valueObj.toString();
                        hitCount.incrementAndGet();
                        return deserializeValue(value);
                    }
                }
            }
            
            missCount.incrementAndGet();
            return null;
        } catch (Exception e) {
            logger.warn("Redis 获取缓存失败，使用降级缓存: key={}", redisKey, e);
            errorCount.incrementAndGet();
            return fallbackCache.get(key);
        }
    }

    @Override
    public V get(K key, Function<K, V> loader) {
        return get(key, loader, defaultExpireTime, defaultTimeUnit);
    }

    @Override
    public V get(K key, Function<K, V> loader, long expireTime, TimeUnit timeUnit) {
        V value = get(key);
        if (value != null) {
            return value;
        }

        try {
            value = loader.apply(key);
            if (value != null) {
                put(key, value, expireTime, timeUnit);
                loadCount.incrementAndGet();
            }
            return value;
        } catch (Exception e) {
            logger.error("加载缓存值失败: key={}", key, e);
            throw new CacheException("加载缓存值失败", e);
        }
    }

    @Override
    public void put(K key, V value) {
        put(key, value, defaultExpireTime, defaultTimeUnit);
    }

    @Override
    public void put(K key, V value, long expireTime, TimeUnit timeUnit) {
        if (key == null || value == null) {
            return;
        }

        String redisKey = buildRedisKey(key);
        String serializedValue = serializeValue(value);

        try {
            if (expireTime <= 0) {
                // 永不过期
                putPermanent(key, value);
            } else {
                // 设置过期时间
                long expireSeconds = timeUnit.toSeconds(expireTime);
                TgCacheHelper.set(redisKey, serializedValue, (int) expireSeconds);
                permanentKeys.remove(redisKey);
            }
        } catch (Exception e) {
            logger.warn("Redis 设置缓存失败，使用降级缓存: key={}", redisKey, e);
            errorCount.incrementAndGet();
            fallbackCache.put(key, value, expireTime, timeUnit);
        }
    }

    @Override
    public void putPermanent(K key, V value) {
        if (key == null || value == null) {
            return;
        }

        String redisKey = buildRedisKey(key);
        String serializedValue = serializeValue(value);

        try {
            TgCacheHelper.set(redisKey, serializedValue);
            permanentKeys.add(redisKey);
        } catch (Exception e) {
            logger.warn("Redis 设置永久缓存失败，使用降级缓存: key={}", redisKey, e);
            errorCount.incrementAndGet();
            fallbackCache.putPermanent(key, value);
        }
    }

    @Override
    public void putAll(Map<K, V> map) {
        putAll(map, defaultExpireTime, defaultTimeUnit);
    }

    @Override
    public void putAll(Map<K, V> map, long expireTime, TimeUnit timeUnit) {
        if (map == null || map.isEmpty()) {
            return;
        }

        for (Map.Entry<K, V> entry : map.entrySet()) {
            put(entry.getKey(), entry.getValue(), expireTime, timeUnit);
        }
    }

    @Override
    public boolean containsKey(K key) {
        if (key == null) {
            return false;
        }

        String redisKey = buildRedisKey(key);
        
        try {
            return TgCacheHelper.hasKey(redisKey);
        } catch (Exception e) {
            logger.warn("Redis 检查键存在失败，使用降级缓存: key={}", redisKey, e);
            errorCount.incrementAndGet();
            return fallbackCache.containsKey(key);
        }
    }

    @Override
    public boolean remove(K key) {
        if (key == null) {
            return false;
        }

        String redisKey = buildRedisKey(key);
        
        try {
            TgCacheHelper.del(redisKey);
            permanentKeys.remove(redisKey);
            return true; // 假设删除成功
        } catch (Exception e) {
            logger.warn("Redis 删除缓存失败，使用降级缓存: key={}", redisKey, e);
            errorCount.incrementAndGet();
            return fallbackCache.remove(key);
        }
    }

    @Override
    public long remove(Set<K> keys) {
        if (keys == null || keys.isEmpty()) {
            return 0;
        }

        long count = 0;
        for (K key : keys) {
            if (remove(key)) {
                count++;
            }
        }
        return count;
    }

    @Override
    public void clear() {
        try {
            // Redis 不支持按前缀批量删除，这里只清理已知的永久键
            for (String key : permanentKeys) {
                TgCacheHelper.del(key);
            }
            permanentKeys.clear();
            logger.info("RedisCacheAdapter 缓存已清空");
        } catch (Exception e) {
            logger.warn("Redis 清空缓存失败，使用降级缓存", e);
            errorCount.incrementAndGet();
            fallbackCache.clear();
        }
    }

    @Override
    public long size() {
        // Redis 无法精确获取大小，返回估算值
        return permanentKeys.size();
    }

    @Override
    public Set<K> keySet() {
        // Redis 无法高效获取所有键，返回空集合
        return ConcurrentHashMap.newKeySet();
    }

    @Override
    public boolean expire(K key, long expireTime, TimeUnit timeUnit) {
        if (key == null) {
            return false;
        }

        String redisKey = buildRedisKey(key);
        
        try {
            long expireSeconds = timeUnit.toSeconds(expireTime);
            TgCacheHelper.expire(redisKey, expireSeconds, timeUnit);
            permanentKeys.remove(redisKey);
            return true; // 假设设置成功
        } catch (Exception e) {
            logger.warn("Redis 设置过期时间失败: key={}", redisKey, e);
            errorCount.incrementAndGet();
            return fallbackCache.expire(key, expireTime, timeUnit);
        }
    }

    @Override
    public long getExpire(K key, TimeUnit timeUnit) {
        if (key == null) {
            return -2;
        }

        String redisKey = buildRedisKey(key);
        
        try {
            if (permanentKeys.contains(redisKey)) {
                return -1; // 永不过期
            }

            // 由于TgCacheHelper没有ttl方法，我们无法精确获取剩余过期时间
            // 只能检查键是否存在
            if (TgCacheHelper.hasKey(redisKey)) {
                // 键存在，但无法获取精确的TTL，返回一个估算值
                return timeUnit.convert(defaultExpireTime, defaultTimeUnit);
            } else {
                return -2; // 键不存在
            }
        } catch (Exception e) {
            logger.warn("Redis 获取过期时间失败: key={}", redisKey, e);
            errorCount.incrementAndGet();
            return fallbackCache.getExpire(key, timeUnit);
        }
    }

    @Override
    public boolean refresh(K key) {
        return expire(key, defaultExpireTime, defaultTimeUnit);
    }

    @Override
    public CacheStats getStats() {
        return new CacheStats(
                hitCount.get(),
                missCount.get(),
                loadCount.get(),
                errorCount.get(),
                size(),
                -1 // Redis 无最大大小限制
        );
    }

    @Override
    public CacheType getType() {
        return CacheType.REDIS;
    }

    @Override
    public boolean isAvailable() {
        try {
            // 简单的可用性检查
            String testKey = keyPrefix + "health_check";
            TgCacheHelper.set(testKey, "test", 1);
            TgCacheHelper.del(testKey);
            return true;
        } catch (Exception e) {
            logger.debug("Redis 不可用: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public void shutdown() {
        clear();
        fallbackCache.shutdown();
        logger.info("RedisCacheAdapter 已关闭");
    }

    /**
     * 构建 Redis 键
     */
    private String buildRedisKey(K key) {
        return keyPrefix + key.toString();
    }

    /**
     * 序列化值
     */
    private String serializeValue(V value) {
        if (value instanceof String) {
            return (String) value;
        }
        return JSON.toJSONString(value);
    }

    /**
     * 反序列化值
     */
    private V deserializeValue(String value) {
        if (!StringUtils.hasText(value)) {
            return null;
        }
        
        if (valueType == String.class) {
            return valueType.cast(value);
        }
        
        try {
            return JSON.parseObject(value, valueType);
        } catch (Exception e) {
            logger.warn("反序列化失败: value={}", value, e);
            return null;
        }
    }

    /**
     * 获取详细统计信息
     */
    public String getDetailedStats() {
        return String.format(
                "RedisCacheAdapter统计: 命中=%d, 未命中=%d, 加载=%d, 错误=%d, 命中率=%.2f%%",
                hitCount.get(),
                missCount.get(),
                loadCount.get(),
                errorCount.get(),
                getHitRate() * 100
        );
    }

    /**
     * 获取命中率
     */
    public double getHitRate() {
        long total = hitCount.get() + missCount.get();
        return total == 0 ? 0.0 : (double) hitCount.get() / total;
    }

    /**
     * 获取降级缓存
     */
    public LocalCacheAdapter<K, V> getFallbackCache() {
        return fallbackCache;
    }
}

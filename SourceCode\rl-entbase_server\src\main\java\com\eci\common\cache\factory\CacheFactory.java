package com.eci.common.cache.factory;

import com.eci.common.cache.adapter.CacheAdapter;
import com.eci.common.cache.adapter.LocalCacheAdapter;
import com.eci.common.cache.adapter.RedisCacheAdapter;
import com.eci.common.cache.config.CacheConfigDetector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 缓存工厂类
 * 根据配置自动创建合适的缓存适配器实例
 */
@Component
public class CacheFactory {

    private static final Logger logger = LoggerFactory.getLogger(CacheFactory.class);

    @Autowired
    private CacheConfigDetector configDetector;

    // 缓存适配器实例池
    private final Map<String, CacheAdapter<?, ?>> cacheInstances = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        logger.info("CacheFactory 初始化完成，当前策略: {}", 
                configDetector.getCurrentStrategy());
    }

    /**
     * 创建字符串类型的缓存适配器
     * 
     * @param cacheName 缓存名称
     * @return 缓存适配器
     */
    public CacheAdapter<String, String> createStringCache(String cacheName) {
        return createCache(cacheName, String.class, String.class);
    }

    /**
     * 创建缓存适配器
     * 
     * @param cacheName 缓存名称
     * @param keyType 键类型
     * @param valueType 值类型
     * @param <K> 键类型
     * @param <V> 值类型
     * @return 缓存适配器
     */
    @SuppressWarnings("unchecked")
    public <K, V> CacheAdapter<K, V> createCache(String cacheName, Class<K> keyType, Class<V> valueType) {
        String cacheKey = buildCacheKey(cacheName, keyType, valueType);
        
        return (CacheAdapter<K, V>) cacheInstances.computeIfAbsent(cacheKey, key -> {
            CacheConfigDetector.CacheConfig config = configDetector.getCacheConfig();
            
            switch (config.getStrategy()) {
                case REDIS:
                    return createRedisCache(cacheName, valueType, config);
                case LOCAL:
                default:
                    return createLocalCache(config);
            }
        });
    }

    /**
     * 创建带自定义配置的缓存适配器
     * 
     * @param cacheName 缓存名称
     * @param keyType 键类型
     * @param valueType 值类型
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @param maxSize 最大大小
     * @param <K> 键类型
     * @param <V> 值类型
     * @return 缓存适配器
     */
    public <K, V> CacheAdapter<K, V> createCache(String cacheName, Class<K> keyType, Class<V> valueType,
                                                long expireTime, TimeUnit timeUnit, long maxSize) {
        String cacheKey = buildCacheKey(cacheName, keyType, valueType, expireTime, timeUnit, maxSize);
        
        return (CacheAdapter<K, V>) cacheInstances.computeIfAbsent(cacheKey, key -> {
            CacheConfigDetector.CacheConfig config = configDetector.getCacheConfig();
            
            switch (config.getStrategy()) {
                case REDIS:
                    return new RedisCacheAdapter<>(cacheName + ":", expireTime, timeUnit, valueType);
                case LOCAL:
                default:
                    return new LocalCacheAdapter<>(maxSize, expireTime, timeUnit, true);
            }
        });
    }

    /**
     * 获取现有的缓存适配器
     * 
     * @param cacheName 缓存名称
     * @param keyType 键类型
     * @param valueType 值类型
     * @param <K> 键类型
     * @param <V> 值类型
     * @return 缓存适配器，如果不存在则返回 null
     */
    @SuppressWarnings("unchecked")
    public <K, V> CacheAdapter<K, V> getCache(String cacheName, Class<K> keyType, Class<V> valueType) {
        String cacheKey = buildCacheKey(cacheName, keyType, valueType);
        return (CacheAdapter<K, V>) cacheInstances.get(cacheKey);
    }

    /**
     * 创建 Redis 缓存适配器
     */
    private <V> CacheAdapter<?, V> createRedisCache(String cacheName, Class<V> valueType, 
                                                   CacheConfigDetector.CacheConfig config) {
        try {
            RedisCacheAdapter<String, V> redisCache = new RedisCacheAdapter<>(
                    cacheName + ":", 
                    config.getLocalCacheExpireMinutes(), 
                    TimeUnit.MINUTES, 
                    valueType
            );
            
            // 测试 Redis 可用性
            if (redisCache.isAvailable()) {
                logger.info("创建 Redis 缓存适配器: {}", cacheName);
                return redisCache;
            } else {
                logger.warn("Redis 不可用，降级到本地缓存: {}", cacheName);
                return createLocalCache(config);
            }
        } catch (Exception e) {
            logger.error("创建 Redis 缓存适配器失败，降级到本地缓存: {}", cacheName, e);
            return createLocalCache(config);
        }
    }

    /**
     * 创建本地缓存适配器
     */
    private <K, V> CacheAdapter<K, V> createLocalCache(CacheConfigDetector.CacheConfig config) {
        return new LocalCacheAdapter<>(
                config.getLocalCacheMaxSize(),
                config.getLocalCacheExpireMinutes(),
                TimeUnit.MINUTES,
                true
        );
    }

    /**
     * 强制刷新缓存策略
     */
    public void refreshCacheStrategy() {
        configDetector.forceRedetect();
        logger.info("缓存策略已刷新: {}", configDetector.getCurrentStrategy());
    }

    /**
     * 切换到指定的缓存策略
     * 
     * @param strategy 目标策略
     */
    public void switchToStrategy(CacheConfigDetector.CacheStrategy strategy) {
        // 清理现有缓存实例
        clearAllCaches();
        
        // 这里可以实现策略切换逻辑
        logger.info("尝试切换到缓存策略: {}", strategy);
        refreshCacheStrategy();
    }

    /**
     * 清理所有缓存实例
     */
    public void clearAllCaches() {
        for (CacheAdapter<?, ?> cache : cacheInstances.values()) {
            try {
                cache.shutdown();
            } catch (Exception e) {
                logger.warn("关闭缓存实例失败", e);
            }
        }
        cacheInstances.clear();
        logger.info("已清理所有缓存实例");
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("缓存工厂统计信息:\n");
        stats.append("当前策略: ").append(configDetector.getCurrentStrategy()).append("\n");
        stats.append("缓存实例数: ").append(cacheInstances.size()).append("\n");
        
        for (Map.Entry<String, CacheAdapter<?, ?>> entry : cacheInstances.entrySet()) {
            CacheAdapter<?, ?> cache = entry.getValue();
            stats.append("- ").append(entry.getKey()).append(": ")
                 .append(cache.getType()).append(", 大小=").append(cache.size())
                 .append(", 可用=").append(cache.isAvailable()).append("\n");
        }
        
        return stats.toString();
    }

    /**
     * 检查指定缓存是否存在
     */
    public boolean cacheExists(String cacheName, Class<?> keyType, Class<?> valueType) {
        String cacheKey = buildCacheKey(cacheName, keyType, valueType);
        return cacheInstances.containsKey(cacheKey);
    }

    /**
     * 移除指定缓存
     */
    public boolean removeCache(String cacheName, Class<?> keyType, Class<?> valueType) {
        String cacheKey = buildCacheKey(cacheName, keyType, valueType);
        CacheAdapter<?, ?> cache = cacheInstances.remove(cacheKey);
        
        if (cache != null) {
            try {
                cache.shutdown();
                logger.info("已移除缓存: {}", cacheKey);
                return true;
            } catch (Exception e) {
                logger.warn("关闭缓存失败: {}", cacheKey, e);
            }
        }
        
        return false;
    }

    /**
     * 构建缓存键
     */
    private String buildCacheKey(String cacheName, Class<?> keyType, Class<?> valueType) {
        return String.format("%s_%s_%s", cacheName, keyType.getSimpleName(), valueType.getSimpleName());
    }

    /**
     * 构建带配置的缓存键
     */
    private String buildCacheKey(String cacheName, Class<?> keyType, Class<?> valueType,
                                long expireTime, TimeUnit timeUnit, long maxSize) {
        return String.format("%s_%s_%s_%d%s_%d", 
                cacheName, keyType.getSimpleName(), valueType.getSimpleName(),
                expireTime, timeUnit.name(), maxSize);
    }

    /**
     * 获取配置检测器
     */
    public CacheConfigDetector getConfigDetector() {
        return configDetector;
    }

    /**
     * 获取所有缓存实例的名称
     */
    public Set<String> getAllCacheNames() {
        return cacheInstances.keySet();
    }

    /**
     * 关闭工厂，释放所有资源
     */
    public void shutdown() {
        clearAllCaches();
        logger.info("CacheFactory 已关闭");
    }
}
